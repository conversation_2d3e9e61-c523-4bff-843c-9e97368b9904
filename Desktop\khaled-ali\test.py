from pynput import mouse, keyboard
import time
from datetime import datetime
import os
import psutil
import json
import threading
try:
    import win32gui
    import win32process
    WINDOWS_AVAILABLE = True
except ImportError:
    WINDOWS_AVAILABLE = False
    print("⚠️ مكتبة win32gui غير متوفرة - سيتم تشغيل النظام بدون مراقبة النوافذ")

# متغيرات تتبع النشاط
last_activity_time = time.time()
session_start_time = time.time()
idle_threshold = 10  # عدد الثواني قبل اعتبار الجهاز "غير نشط"
total_active_time = 0
last_status = "active"  # لتتبع آخر حالة (active/idle)
activity_log = []  # لحفظ سجل النشاطات

# متغيرات جديدة لمراقبة التطبيقات
current_window = ""
last_window = ""
window_log = []  # سجل النوافذ والتطبيقات
apps_usage = {}  # إحصائيات استخدام التطبيقات
websites_visited = []  # المواقع المزارة
last_save_time = time.time()  # آخر وقت حفظ
save_interval = 60  # حفظ كل دقيقة

def get_active_window():
    """الحصول على النافذة النشطة حالياً"""
    if not WINDOWS_AVAILABLE:
        return "غير متوفر", "غير متوفر"

    try:
        hwnd = win32gui.GetForegroundWindow()
        window_title = win32gui.GetWindowText(hwnd)
        _, pid = win32process.GetWindowThreadProcessId(hwnd)
        process = psutil.Process(pid)
        app_name = process.name()
        return window_title, app_name
    except:
        return "غير معروف", "غير معروف"

def log_window_activity():
    """تسجيل نشاط النوافذ والتطبيقات"""
    global current_window, last_window, window_log, apps_usage

    window_title, app_name = get_active_window()
    current_window = f"{app_name} - {window_title}"

    if current_window != last_window and current_window != " - ":
        timestamp = datetime.now().strftime("%H:%M:%S")
        window_log.append(f"{timestamp} - فتح: {current_window}")

        # تحديث إحصائيات التطبيقات
        if app_name in apps_usage:
            apps_usage[app_name] += 1
        else:
            apps_usage[app_name] = 1

        # تسجيل المواقع إذا كان متصفح
        if any(browser in app_name.lower() for browser in ['chrome', 'firefox', 'edge', 'safari', 'opera']):
            if 'http' in window_title.lower() or any(domain in window_title.lower() for domain in ['.com', '.org', '.net', '.gov']):
                websites_visited.append(f"{timestamp} - زيارة: {window_title}")

        last_window = current_window

def save_periodic_data():
    """حفظ البيانات بشكل دوري كل دقيقة"""
    try:
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        filename = f"activity_log_{timestamp}.json"

        data = {
            "timestamp": timestamp,
            "session_duration": time.time() - session_start_time,
            "active_time": total_active_time,
            "current_status": last_status,
            "current_window": current_window,
            "apps_usage": apps_usage,
            "recent_activities": activity_log[-10:],  # آخر 10 نشاطات
            "recent_windows": window_log[-10:],  # آخر 10 نوافذ
            "websites_visited": websites_visited[-5:]  # آخر 5 مواقع
        }

        with open(filename, 'w', encoding='utf-8') as file:
            json.dump(data, file, ensure_ascii=False, indent=2)

        print(f"💾 تم حفظ البيانات الدورية: {filename}")
        return filename
    except Exception as e:
        print(f"❌ خطأ في الحفظ الدوري: {e}")
        return None

def save_work_data():
    """حفظ بيانات العمل في ملف .txt"""
    try:
        session_end_time = time.time()
        total_session_time = session_end_time - session_start_time

        # إنشاء اسم الملف بالتاريخ والوقت
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        filename = f"work_hours_{timestamp}.txt"

        with open(filename, 'w', encoding='utf-8') as file:
            file.write("=" * 60 + "\n")
            file.write("تقرير مفصل لساعات العمل ونشاط الموظف\n")
            file.write("=" * 60 + "\n\n")

            # معلومات الجلسة
            file.write("📊 معلومات الجلسة:\n")
            file.write("-" * 30 + "\n")
            file.write(f"تاريخ الجلسة: {datetime.fromtimestamp(session_start_time).strftime('%Y-%m-%d')}\n")
            file.write(f"وقت البداية: {datetime.fromtimestamp(session_start_time).strftime('%H:%M:%S')}\n")
            file.write(f"وقت النهاية: {datetime.fromtimestamp(session_end_time).strftime('%H:%M:%S')}\n")
            file.write(f"إجمالي وقت الجلسة: {total_session_time/3600:.2f} ساعة ({total_session_time/60:.1f} دقيقة)\n")
            file.write(f"إجمالي وقت النشاط: {total_active_time/3600:.2f} ساعة ({total_active_time/60:.1f} دقيقة)\n")
            file.write(f"نسبة النشاط: {(total_active_time/total_session_time)*100:.1f}%\n\n")

            # إحصائيات التطبيقات
            file.write("💻 التطبيقات المستخدمة:\n")
            file.write("-" * 30 + "\n")
            if apps_usage:
                sorted_apps = sorted(apps_usage.items(), key=lambda x: x[1], reverse=True)
                for app, count in sorted_apps:
                    file.write(f"{app}: {count} مرة\n")
            else:
                file.write("لا توجد بيانات تطبيقات\n")
            file.write("\n")

            # المواقع المزارة
            file.write("🌐 المواقع المزارة:\n")
            file.write("-" * 30 + "\n")
            if websites_visited:
                for website in websites_visited:
                    file.write(f"{website}\n")
            else:
                file.write("لا توجد مواقع مسجلة\n")
            file.write("\n")

            # سجل النوافذ والتطبيقات
            file.write("🪟 سجل النوافذ والتطبيقات:\n")
            file.write("-" * 30 + "\n")
            for window_entry in window_log:
                file.write(f"{window_entry}\n")
            file.write("\n")

            # سجل النشاطات
            file.write("⚡ سجل النشاطات:\n")
            file.write("-" * 30 + "\n")
            for log_entry in activity_log:
                file.write(f"{log_entry}\n")

        print(f"✅ تم حفظ البيانات في الملف: {filename}")
        return filename
    except Exception as e:
        print(f"❌ خطأ في حفظ البيانات: {e}")
        return None

def log_activity(status, idle_time=0):
    """تسجيل النشاط في السجل"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    if status == "active":
        activity_log.append(f"{timestamp} - المستخدم نشط")
    else:
        activity_log.append(f"{timestamp} - المستخدم غير نشط منذ {int(idle_time)} ثانية")

def on_mouse_move(x, y):
    global last_activity_time, last_status, total_active_time
    current_time = time.time()

    # إذا كان المستخدم كان غير نشط، احسب الوقت النشط
    if last_status == "idle":
        last_status = "active"
        log_activity("active")

    last_activity_time = current_time

def on_click(x, y, button, pressed):
    global last_activity_time, last_status
    current_time = time.time()

    if last_status == "idle":
        last_status = "active"
        log_activity("active")

    last_activity_time = current_time

def on_scroll(x, y, dx, dy):
    global last_activity_time, last_status
    current_time = time.time()

    if last_status == "idle":
        last_status = "active"
        log_activity("active")

    last_activity_time = current_time

def on_key_press(key):
    global last_activity_time, last_status
    current_time = time.time()

    if last_status == "idle":
        last_status = "active"
        log_activity("active")

    last_activity_time = current_time

# تشغيل مراقبة الماوس والكيبورد
mouse_listener = mouse.Listener(
    on_move=on_mouse_move,
    on_click=on_click,
    on_scroll=on_scroll)
keyboard_listener = keyboard.Listener(on_press=on_key_press)

mouse_listener.start()
keyboard_listener.start()

# حلقة تفقد النشاط
print("🚀 بدء مراقبة النشاط...")
print("اضغط Ctrl+C لإيقاف المراقبة وحفظ البيانات")
print("البرنامج يعمل الآن...")

try:
    while True:
        current_time = time.time()
        idle_time = current_time - last_activity_time

        # مراقبة النوافذ والتطبيقات
        log_window_activity()

        if idle_time > idle_threshold:
            if last_status == "active":
                # المستخدم أصبح غير نشط
                last_status = "idle"
                log_activity("idle", idle_time)
            print(f"🛑 المستخدم غير نشط منذ {int(idle_time)} ثانية | النافذة الحالية: {current_window[:50]}...")
        else:
            if last_status == "idle":
                # المستخدم أصبح نشط
                last_status = "active"
                log_activity("active")

            # إضافة الوقت النشط
            total_active_time += 2  # نضيف ثانيتين (مدة النوم)
            print(f"✅ المستخدم نشط، الخمول = {int(idle_time)} ثانية | النافذة الحالية: {current_window[:50]}...")

        # حفظ دوري كل دقيقة
        if current_time - last_save_time >= save_interval:
            save_periodic_data()
            last_save_time = current_time

        time.sleep(2)

except KeyboardInterrupt:
    print("\n⏹️ تم إيقاف المراقبة...")
    print("💾 جاري حفظ البيانات...")

    # حفظ البيانات النهائية
    filename = save_work_data()
    if filename:
        print(f"✅ تم حفظ تقرير العمل في: {filename}")

    print("👋 شكراً لاستخدام نظام مراقبة ساعات العمل!")
