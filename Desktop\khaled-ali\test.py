from pynput import mouse, keyboard
import time
from datetime import datetime
import os
import psutil


try:
    import win32gui
    import win32process
    WINDOWS_AVAILABLE = True
except ImportError:
    WINDOWS_AVAILABLE = False
    print("⚠️ win32gui library not available - system will run without window monitoring")

# Activity tracking variables
last_activity_time = time.time()
session_start_time = time.time()
idle_threshold = 10  # seconds before considering device "inactive"
total_active_time = 0
last_status = "active"  # to track last status (active/idle)
activity_log = []  # to save activity log

# New variables for application monitoring
current_window = ""
last_window = ""
window_log = []  # window and application log
apps_usage = {}  # application usage statistics
websites_visited = []  # visited websites
last_save_time = time.time()  # last save time
save_interval = 60  # save every minute

def get_active_window():
    """Get the currently active window"""
    if not WINDOWS_AVAILABLE:
        return "Not Available", "Not Available"

    try:
        hwnd = win32gui.GetForegroundWindow()
        window_title = win32gui.GetWindowText(hwnd)
        _, pid = win32process.GetWindowThreadProcessId(hwnd)
        process = psutil.Process(pid)
        app_name = process.name()
        return window_title, app_name
    except:
        return "Unknown", "Unknown"

def log_window_activity():
    """Log window and application activity"""
    global current_window, last_window, window_log, apps_usage

    window_title, app_name = get_active_window()
    current_window = f"{app_name} - {window_title}"

    if current_window != last_window and current_window != " - ":
        timestamp = datetime.now().strftime("%H:%M:%S")
        window_log.append(f"{timestamp} - Opened: {current_window}")

        # Update application statistics
        if app_name in apps_usage:
            apps_usage[app_name] += 1
        else:
            apps_usage[app_name] = 1

        # Log websites if it's a browser
        if any(browser in app_name.lower() for browser in ['chrome', 'firefox', 'edge', 'safari', 'opera']):
            if 'http' in window_title.lower() or any(domain in window_title.lower() for domain in ['.com', '.org', '.net', '.gov']):
                websites_visited.append(f"{timestamp} - Visited: {window_title}")

        last_window = current_window

def save_periodic_data():
    """Save data periodically every minute"""
    try:
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        # Use absolute path to ensure file is saved in the correct directory
        current_dir = os.path.dirname(os.path.abspath(__file__))
        filename = os.path.join(current_dir, f"activity_log_{timestamp}.txt")

        session_duration = time.time() - session_start_time

        print(f"🔄 Attempting to save periodic data to: {filename}")
        with open(filename, 'w', encoding='utf-8') as file:
            file.write("=" * 50 + "\n")
            file.write("PERIODIC ACTIVITY REPORT\n")
            file.write("=" * 50 + "\n\n")

            file.write(f"Timestamp: {timestamp}\n")
            file.write(f"Session Duration: {session_duration/3600:.2f} hours ({session_duration/60:.1f} minutes)\n")
            file.write(f"Active Time: {total_active_time/3600:.2f} hours ({total_active_time/60:.1f} minutes)\n")
            file.write(f"Current Status: {last_status}\n")
            file.write(f"Current Window: {current_window}\n\n")

            file.write("📊 Application Usage:\n")
            file.write("-" * 25 + "\n")
            if apps_usage:
                for app, count in apps_usage.items():
                    file.write(f"{app}: {count} times\n")
            else:
                file.write("No application data\n")
            file.write("\n")

            file.write("⚡ Recent Activities (Last 10):\n")
            file.write("-" * 25 + "\n")
            for activity in activity_log[-10:]:
                file.write(f"{activity}\n")
            file.write("\n")

            file.write("🪟 Recent Windows (Last 10):\n")
            file.write("-" * 25 + "\n")
            for window in window_log[-10:]:
                file.write(f"{window}\n")
            file.write("\n")

            file.write("🌐 Recent Websites (Last 5):\n")
            file.write("-" * 25 + "\n")
            for website in websites_visited[-5:]:
                file.write(f"{website}\n")

        print(f"💾 Periodic data saved: {filename}")
        return filename
    except Exception as e:
        print(f"❌ Error in periodic save: {e}")
        return None

def save_work_data():
    """Save work data to .txt file"""
    try:
        session_end_time = time.time()
        total_session_time = session_end_time - session_start_time

        # Create filename with date and time using absolute path
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        current_dir = os.path.dirname(os.path.abspath(__file__))
        filename = os.path.join(current_dir, f"work_hours_{timestamp}.txt")

        print(f"🔄 Attempting to save work data to: {filename}")
        with open(filename, 'w', encoding='utf-8') as file:
            file.write("=" * 60 + "\n")
            file.write("DETAILED EMPLOYEE WORK HOURS AND ACTIVITY REPORT\n")
            file.write("=" * 60 + "\n\n")

            # Session information
            file.write("📊 Session Information:\n")
            file.write("-" * 30 + "\n")
            file.write(f"Session Date: {datetime.fromtimestamp(session_start_time).strftime('%Y-%m-%d')}\n")
            file.write(f"Start Time: {datetime.fromtimestamp(session_start_time).strftime('%H:%M:%S')}\n")
            file.write(f"End Time: {datetime.fromtimestamp(session_end_time).strftime('%H:%M:%S')}\n")
            file.write(f"Total Session Time: {total_session_time/3600:.2f} hours ({total_session_time/60:.1f} minutes)\n")
            file.write(f"Total Active Time: {total_active_time/3600:.2f} hours ({total_active_time/60:.1f} minutes)\n")
            file.write(f"Activity Percentage: {(total_active_time/total_session_time)*100:.1f}%\n\n")

            # Application statistics
            file.write("💻 Applications Used:\n")
            file.write("-" * 30 + "\n")
            if apps_usage:
                sorted_apps = sorted(apps_usage.items(), key=lambda x: x[1], reverse=True)
                for app, count in sorted_apps:
                    file.write(f"{app}: {count} times\n")
            else:
                file.write("No application data available\n")
            file.write("\n")

            # Visited websites
            file.write("🌐 Websites Visited:\n")
            file.write("-" * 30 + "\n")
            if websites_visited:
                for website in websites_visited:
                    file.write(f"{website}\n")
            else:
                file.write("No websites recorded\n")
            file.write("\n")

            # Window and application log
            file.write("🪟 Windows and Applications Log:\n")
            file.write("-" * 30 + "\n")
            for window_entry in window_log:
                file.write(f"{window_entry}\n")
            file.write("\n")

            # Activity log
            file.write("⚡ Activity Log:\n")
            file.write("-" * 30 + "\n")
            for log_entry in activity_log:
                file.write(f"{log_entry}\n")

        print(f"✅ Data saved to file: {filename}")
        return filename
    except Exception as e:
        print(f"❌ Error saving data: {e}")
        return None

def log_activity(status, idle_time=0):
    """Log activity in the log"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    if status == "active":
        activity_log.append(f"{timestamp} - User is active")
    else:
        activity_log.append(f"{timestamp} - User has been idle for {int(idle_time)} seconds")

def on_mouse_move(x, y):
    global last_activity_time, last_status, total_active_time
    current_time = time.time()

    # If user was idle, calculate active time
    if last_status == "idle":
        last_status = "active"
        log_activity("active")

    last_activity_time = current_time

def on_click(x, y, button, pressed):
    global last_activity_time, last_status
    current_time = time.time()

    if last_status == "idle":
        last_status = "active"
        log_activity("active")

    last_activity_time = current_time

def on_scroll(x, y, dx, dy):
    global last_activity_time, last_status
    current_time = time.time()

    if last_status == "idle":
        last_status = "active"
        log_activity("active")

    last_activity_time = current_time

def on_key_press(key):
    global last_activity_time, last_status
    current_time = time.time()

    if last_status == "idle":
        last_status = "active"
        log_activity("active")

    last_activity_time = current_time

# Start mouse and keyboard monitoring
mouse_listener = mouse.Listener(
    on_move=on_mouse_move,
    on_click=on_click,
    on_scroll=on_scroll)
keyboard_listener = keyboard.Listener(on_press=on_key_press)

mouse_listener.start()
keyboard_listener.start()

# Activity monitoring loop
print("🚀 Starting activity monitoring...")
print("Press Ctrl+C to stop monitoring and save data")
print("Program is now running...")

try:
    while True:
        current_time = time.time()
        idle_time = current_time - last_activity_time

        # Monitor windows and applications
        log_window_activity()

        if idle_time > idle_threshold:
            if last_status == "active":
                # User became inactive
                last_status = "idle"
                log_activity("idle", idle_time)
            print(f"🛑 User has been idle for {int(idle_time)} seconds | Current window: {current_window[:50]}...")
        else:
            if last_status == "idle":
                # User became active
                last_status = "active"
                log_activity("active")

            # Add active time
            total_active_time += 2  # Add 2 seconds (sleep duration)
            print(f"✅ User is active, idle time = {int(idle_time)} seconds | Current window: {current_window[:50]}...")

        # Periodic save every minute
        if current_time - last_save_time >= save_interval:
            save_periodic_data()
            last_save_time = current_time

        time.sleep(2)

except KeyboardInterrupt:
    print("\n⏹️ Monitoring stopped...")
    print("💾 Saving data...")

    # Save final data
    filename = save_work_data()
    if filename:
        print(f"✅ Work report saved to: {filename}")

    print("👋 Thank you for using the work hours monitoring system!")


